#!/usr/bin/env python3
"""
Examples showing how to add Langfuse tracing to existing controller functions.

This demonstrates how to enhance your existing SmartHR controllers with
comprehensive LLM observability without major code changes.
"""

from typing import List, Optional
from fastapi import HTTPException
from langchain_core.messages import HumanMessage

# Import existing functions (these would be your actual imports)
from models.llm import inference_with_fallback
from utils.match_evaluations import evaluate_candidate
from utils.langfuse_utils import LangfuseManager


def enhanced_candidate_match_positions(
    candidate_id: str,
    user_id: Optional[str] = None,
    session_id: Optional[str] = None
):
    """
    Enhanced version of candidate matching with Langfuse tracing.
    
    This shows how to add tracing to your existing candidate matching logic.
    """
    
    # Create a session ID if not provided
    if not session_id:
        session_id = f"candidate-matching-{candidate_id}"
    
    # Use trace context for the entire operation
    with LangfuseManager.trace_context(
        name="candidate-position-matching",
        user_id=user_id,
        session_id=session_id,
        tags=["candidate-matching", "position-recommendation"],
        input_data={"candidate_id": candidate_id}
    ) as span:
        
        try:
            # Your existing database logic here
            # candidate = get_candidate_by_id(candidate_id)
            # positions = get_all_active_positions()
            
            # Simulate getting candidate and positions
            candidate_text = "Sample candidate profile..."
            positions = [
                {"id": "pos-1", "text": "Position 1 description..."},
                {"id": "pos-2", "text": "Position 2 description..."},
            ]
            
            matches = []
            
            # Evaluate each position with tracing
            for position in positions:
                match_result = evaluate_candidate(
                    candidate=candidate_text,
                    position=position["text"],
                    user_id=user_id,
                    session_id=session_id,
                    candidate_id=candidate_id,
                    position_id=position["id"]
                )
                
                matches.append({
                    "position_id": position["id"],
                    "score": match_result.get("Score", 0),
                    "analysis": match_result.get("LLM_Analysis", {})
                })
            
            # Sort by score
            matches.sort(key=lambda x: x["score"], reverse=True)
            
            # Update trace with results
            if span:
                span.update_trace(
                    output={
                        "matches_found": len(matches),
                        "top_score": matches[0]["score"] if matches else 0,
                        "positions_evaluated": len(positions)
                    }
                )
            
            return matches
            
        except Exception as e:
            if span:
                span.update_trace(output={"error": str(e), "status": "failed"})
            raise HTTPException(status_code=500, detail=str(e))


def enhanced_interview_evaluation(
    interview_id: str,
    user_id: Optional[str] = None,
    session_id: Optional[str] = None
):
    """
    Enhanced interview evaluation with comprehensive tracing.
    """
    
    if not session_id:
        session_id = f"interview-evaluation-{interview_id}"
    
    with LangfuseManager.trace_context(
        name="interview-evaluation-pipeline",
        user_id=user_id,
        session_id=session_id,
        tags=["interview", "evaluation", "scoring"],
        input_data={"interview_id": interview_id}
    ) as span:
        
        try:
            # Step 1: Extract answers from transcript
            transcript = "Sample interview transcript..."
            questions = ["Question 1?", "Question 2?", "Question 3?"]
            
            extracted_answers = inference_with_fallback(
                task_prompt="Extract and structure the candidate's answers to the interview questions.",
                user_messages=[
                    HumanMessage(content=f"Questions: {questions}"),
                    HumanMessage(content=f"Transcript: {transcript}")
                ],
                user_id=user_id,
                session_id=session_id,
                trace_tags=["answer-extraction", "transcript-processing"],
                trace_name="interview-answer-extraction"
            )
            
            # Step 2: Evaluate technical competency
            technical_evaluation = inference_with_fallback(
                task_prompt="Evaluate the candidate's technical competency based on their interview answers.",
                user_messages=[HumanMessage(content=str(extracted_answers))],
                user_id=user_id,
                session_id=session_id,
                trace_tags=["technical-evaluation", "competency-assessment"],
                trace_name="technical-competency-evaluation"
            )
            
            # Step 3: Evaluate soft skills
            soft_skills_evaluation = inference_with_fallback(
                task_prompt="Evaluate the candidate's communication and soft skills based on the interview.",
                user_messages=[HumanMessage(content=str(extracted_answers))],
                user_id=user_id,
                session_id=session_id,
                trace_tags=["soft-skills", "communication-assessment"],
                trace_name="soft-skills-evaluation"
            )
            
            # Combine results
            final_evaluation = {
                "interview_id": interview_id,
                "technical_score": 85,  # Would be extracted from technical_evaluation
                "soft_skills_score": 78,  # Would be extracted from soft_skills_evaluation
                "overall_recommendation": "Strong candidate",
                "extracted_answers": extracted_answers,
                "technical_analysis": technical_evaluation,
                "soft_skills_analysis": soft_skills_evaluation
            }
            
            # Update trace with final results
            if span:
                span.update_trace(
                    output={
                        "technical_score": final_evaluation["technical_score"],
                        "soft_skills_score": final_evaluation["soft_skills_score"],
                        "recommendation": final_evaluation["overall_recommendation"],
                        "evaluation_completed": True
                    }
                )
            
            return final_evaluation
            
        except Exception as e:
            if span:
                span.update_trace(output={"error": str(e), "status": "failed"})
            raise HTTPException(status_code=500, detail=str(e))


def enhanced_position_creation(
    position_raw_text: str,
    project_id: str,
    user_id: Optional[str] = None,
    session_id: Optional[str] = None
):
    """
    Enhanced position creation with full pipeline tracing.
    """
    
    if not session_id:
        session_id = f"position-creation-{project_id}"
    
    with LangfuseManager.trace_context(
        name="position-creation-pipeline",
        user_id=user_id,
        session_id=session_id,
        tags=["position-creation", "job-posting", "requirements-extraction"],
        input_data={
            "project_id": project_id,
            "raw_text_length": len(position_raw_text)
        }
    ) as span:
        
        try:
            # Step 1: Transform raw text to structured JSON
            structured_position = inference_with_fallback(
                task_prompt="Transform this raw job posting into structured JSON with title, requirements, responsibilities, and qualifications.",
                user_messages=[HumanMessage(content=position_raw_text)],
                user_id=user_id,
                session_id=session_id,
                trace_tags=["position-structuring", "json-extraction"],
                trace_name="position-structure-extraction"
            )
            
            # Step 2: Generate embedding-optimized text
            embedding_text = inference_with_fallback(
                task_prompt="Create a clean, optimized version of this job posting for candidate matching and embeddings.",
                user_messages=[HumanMessage(content=position_raw_text)],
                user_id=user_id,
                session_id=session_id,
                trace_tags=["embedding-optimization", "matching-prep"],
                trace_name="embedding-text-generation"
            )
            
            # Step 3: Extract key skills
            key_skills = inference_with_fallback(
                task_prompt="Extract the top 10 most important skills and technologies mentioned in this job posting.",
                user_messages=[HumanMessage(content=position_raw_text)],
                user_id=user_id,
                session_id=session_id,
                trace_tags=["skills-extraction", "requirements-analysis"],
                trace_name="key-skills-extraction"
            )
            
            # Step 4: Generate interview questions
            interview_questions = inference_with_fallback(
                task_prompt="Generate 5 relevant interview questions based on this job posting.",
                user_messages=[HumanMessage(content=position_raw_text)],
                user_id=user_id,
                session_id=session_id,
                trace_tags=["question-generation", "interview-prep"],
                trace_name="interview-questions-generation"
            )
            
            # Combine all results
            position_data = {
                "project_id": project_id,
                "structured_info": structured_position,
                "embedding_text": embedding_text,
                "key_skills": key_skills,
                "interview_questions": interview_questions,
                "raw_text": position_raw_text
            }
            
            # Update trace with comprehensive results
            if span:
                span.update_trace(
                    output={
                        "position_created": True,
                        "structured_data_extracted": bool(structured_position),
                        "embedding_text_generated": bool(embedding_text),
                        "skills_extracted": bool(key_skills),
                        "questions_generated": bool(interview_questions),
                        "pipeline_steps_completed": 4
                    }
                )
            
            return position_data
            
        except Exception as e:
            if span:
                span.update_trace(output={"error": str(e), "status": "failed"})
            raise HTTPException(status_code=500, detail=str(e))


def enhanced_bulk_candidate_processing(
    candidate_ids: List[str],
    position_id: str,
    user_id: Optional[str] = None,
    session_id: Optional[str] = None
):
    """
    Enhanced bulk candidate processing with batch tracing.
    """
    
    if not session_id:
        session_id = f"bulk-processing-{position_id}"
    
    with LangfuseManager.trace_context(
        name="bulk-candidate-processing",
        user_id=user_id,
        session_id=session_id,
        tags=["bulk-processing", "candidate-evaluation", "batch-analysis"],
        input_data={
            "position_id": position_id,
            "candidate_count": len(candidate_ids)
        }
    ) as span:
        
        try:
            results = []
            
            # Process candidates in batches for efficiency
            batch_size = 5
            for i in range(0, len(candidate_ids), batch_size):
                batch = candidate_ids[i:i + batch_size]
                
                # Simulate getting candidate data
                candidates_text = [f"Candidate {cid} profile..." for cid in batch]
                position_text = "Sample position description..."
                
                # Use batch evaluation with tracing
                from utils.match_evaluations import evaluate_candidates_batch
                batch_result = evaluate_candidates_batch(
                    candidates=candidates_text,
                    position=position_text,
                    user_id=user_id,
                    session_id=session_id,
                    position_id=position_id
                )
                
                results.extend(batch_result.candidates_analysis if hasattr(batch_result, 'candidates_analysis') else [])
            
            # Sort results by score
            results.sort(key=lambda x: getattr(x, 'Score', 0), reverse=True)
            
            # Update trace with final results
            if span:
                span.update_trace(
                    output={
                        "candidates_processed": len(candidate_ids),
                        "batches_completed": (len(candidate_ids) + batch_size - 1) // batch_size,
                        "top_score": getattr(results[0], 'Score', 0) if results else 0,
                        "processing_completed": True
                    }
                )
            
            return {
                "position_id": position_id,
                "results": results,
                "total_processed": len(candidate_ids)
            }
            
        except Exception as e:
            if span:
                span.update_trace(output={"error": str(e), "status": "failed"})
            raise HTTPException(status_code=500, detail=str(e))


def demo_enhanced_controllers():
    """
    Demonstrate the enhanced controllers with tracing.
    """
    print("🎯 SmartHR Enhanced Controllers with Langfuse Tracing")
    print("=" * 60)
    
    try:
        # Initialize Langfuse
        if not LangfuseManager.initialize():
            print("⚠️  Langfuse not configured. Running without tracing.")
        
        # Demo user context
        user_id = "demo-user-123"
        session_id = "demo-session-456"
        
        print("\n1. Enhanced Candidate Matching...")
        matches = enhanced_candidate_match_positions(
            candidate_id="candidate-789",
            user_id=user_id,
            session_id=session_id
        )
        print(f"   Found {len(matches)} position matches")
        
        print("\n2. Enhanced Interview Evaluation...")
        evaluation = enhanced_interview_evaluation(
            interview_id="interview-101",
            user_id=user_id,
            session_id=session_id
        )
        print(f"   Technical Score: {evaluation['technical_score']}")
        
        print("\n3. Enhanced Position Creation...")
        position = enhanced_position_creation(
            position_raw_text="Senior Python Developer - 5+ years experience required...",
            project_id="project-202",
            user_id=user_id,
            session_id=session_id
        )
        print("   Position created with full pipeline tracing")
        
        print("\n4. Enhanced Bulk Processing...")
        bulk_results = enhanced_bulk_candidate_processing(
            candidate_ids=["c1", "c2", "c3", "c4", "c5"],
            position_id="position-303",
            user_id=user_id,
            session_id=session_id
        )
        print(f"   Processed {bulk_results['total_processed']} candidates")
        
        print("\n" + "=" * 60)
        print("✅ All enhanced controllers demonstrated!")
        print("Check your Langfuse dashboard for comprehensive traces.")
        
        # Flush events
        LangfuseManager.flush()
        
    except Exception as e:
        print(f"❌ Error in demo: {str(e)}")
    
    finally:
        LangfuseManager.shutdown()


if __name__ == "__main__":
    demo_enhanced_controllers()
