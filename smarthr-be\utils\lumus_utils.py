import io
import os
import tempfile
import httpx
import psycopg2
import logging
from fastapi import UploadFile, HTTPException
from models.candidate import UploadFileResponse
from PyPDF2 import PdfReader


logger = logging.getLogger(__name__)
LUMUS_URL = os.getenv("LUMUS_API_URL", "localhost")
API_CALLBACK_URL = os.getenv("API_CALLBACK_URL", "localhost")


# Process a single file using the Lumus API.
# This function reads the file, converts it to PDF if necessary, and sends it to the Lumus API for processing.
async def process_file_lumus(file: UploadFile) -> UploadFileResponse:
    """
    Process a single file using the Lumus API.

    Args:
        file (UploadFile): The file to process.

    Returns:
        UploadFileResponse: The response after processing the file.
    """
    if not file:
        return UploadFileResponse(
            candidate_info={},
            error=True,
            error_message="No file provided"
        )
    timeout_seconds = float(os.getenv("LUMUS_API_TIMEOUT", "320.0"))
    timeout = httpx.Timeout(timeout_seconds)
    form_data, num_pages = await get_form_data_lumus(file)

    if not form_data:
        return UploadFileResponse(
            candidate_info={},
            error=True,
            error_message="No file content provided"
        )

    if num_pages > 6:
        form_data["callback_url"] = (None, API_CALLBACK_URL)
        return await process_file_lumus_test(form_data)

    async with httpx.AsyncClient(timeout=timeout) as client:
        try:
            result = await client.post(LUMUS_URL + "/process", files=form_data)
            if result.status_code == 200:
                candInfo = result.json().get("response")
                if candInfo is None or candInfo.get("personal_info") is None:
                    return UploadFileResponse(
                        candidate_info=candInfo,
                        error=True,
                        error_message="personal_info not found in file"
                    )
                if candInfo.get("personal_info").get("email") is None:
                    return UploadFileResponse(
                        candidate_info=candInfo,
                        error=True,
                        error_message="email not found in file"
                    )
                if candInfo.get("work_experience") is None:
                    return UploadFileResponse(
                        candidate_info=candInfo,
                        error=True,
                        error_message="work_experience not found in file"
                    )
                return UploadFileResponse(
                    candidate_info=candInfo,
                    error=False,
                    error_message=""
                )
            else:
                return UploadFileResponse(
                    candidate_info={},
                    error=True,
                    error_message=str(result.status_code) + " " + result.text
                )
        except Exception as e:
            return UploadFileResponse(
                candidate_info={},
                error=True,
                error_message="Error calling Lumus. " + str(e)
            )


# Prepare form data for the Lumus API.
# This function reads the file content, converts DOCX files to PDF if necessary, and prepares the form data for the API request.
async def get_form_data_lumus(file: UploadFile):
    """
    Convert file to PDF if needed and prepare form data for Lumus API.

    Args:
        file (UploadFile): The file to process.

    Returns:
        dict: Form data for the API.
    """
    from utils.file_utils import convert_docx_to_pdf  # Import here to avoid circular import
    try:
        file_content = await file.read()
        ext = file.filename.split('.')[-1].lower()

        num_pages = None

        with tempfile.TemporaryDirectory() as tmpdir:
            original_path = os.path.join(tmpdir, file.filename)
            with open(original_path, "wb") as f:
                f.write(file_content)

                if file.content_type == "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
                    pdf_path = os.path.join(tmpdir, file.filename.replace(".docx", ".pdf"))
                    pdf_path = convert_docx_to_pdf(original_path, pdf_path)
                    with open(pdf_path, "rb") as pdf_file:
                        converted_content = pdf_file.read()
                    pdf_reader = PdfReader(io.BytesIO(converted_content))
                    num_pages = len(pdf_reader.pages)
                    form_data = {
                        "file": ("file.pdf", converted_content, "application/pdf"),
                        "action": (None, "cv")
                    }
                else:
                    pdf_reader = PdfReader(io.BytesIO(file_content))
                    num_pages = len(pdf_reader.pages)
                    form_data = {
                        "file": (f"file.{ext}", file_content, file.content_type),
                        "action": (None, "cv")
                    }

                print(f"Number of pages: {num_pages}")
        return form_data, num_pages
    except psycopg2.Error as e:
        logger.error(f"Database error in get_form_data: {str(e)}")
        return HTTPException(status_code=500, detail=f"Database Error in get_form_data: {str(e)}")
    except HTTPException as e:
        logger.error(f"HTTPException in get_form_data: {str(e.detail)}")
        return e
    except Exception as e:
        logger.error(f"Unexpected error in get_form_data: {str(e)}")
        return HTTPException(status_code=500, detail=f"Unexpected Error in get_form_data: {str(e)}")


# Extract data from multiple files using the Lumus API.
# This function processes each file concurrently and returns a list of responses.
async def extract_data_lumus(files):
    """
    Process multiple files concurrently using the Lumus API.

    Args:
        files (List[UploadFile]): List of files to process.

    Returns:
        List[UploadFileResponse]: List of processed file responses.
    """
    tasks = [process_file_lumus(file) for file in files]
    import asyncio
    return await asyncio.gather(*tasks)


# make a post to https://apilumusaideveu.azurewebsites.net/process-test with the file
async def process_file_lumus_test(form_data: dict) -> UploadFileResponse:
    """
    Process a single file using the Lumus API.

    Args:
        file (UploadFile): The file to process.

    Returns:
        UploadFileResponse: The response after processing the file.
    """
    if not form_data:
        return UploadFileResponse(
            candidate_info={},
            error=True,
            error_message="No form_data provided"
        )
    timeout_seconds = float(os.getenv("LUMUS_API_TIMEOUT", "320.0"))
    timeout = httpx.Timeout(timeout_seconds)
    async with httpx.AsyncClient(timeout=timeout) as client:
        try:
            # form_data = {
            #     "file": (file.filename, await file.read(), file.content_type),
            #     "action": (None, "cv"),
            #     "callback_url": (None, "https://apismarthrdevuse2.azurewebsites.net/lumus/callback")
            # }
            result = await client.post(LUMUS_URL + "/process-test", files=form_data)
            if result.status_code == 200:
                print("result", result.json())
                candInfo = dict(result.json() or {})
                print("candInfo", candInfo)
                return UploadFileResponse(
                    candidate_info=candInfo,
                    error=True,
                    error_message="PROCESING"
                )
            else:
                return UploadFileResponse(
                    candidate_info={},
                    error=True,
                    error_message=str(result.status_code) + " " + result.text
                )
        except Exception as e:
            return UploadFileResponse(
                candidate_info={},
                error=True,
                error_message="Error calling Lumus. " + str(e)
            )


# GET RESUL FROM LUMUS /task/result/cv-1756778960-48d029c4
async def get_lumus_result(task_id: str) -> dict:
    """
    Get the result of a Lumus task.

    Args:
        task_id (str): The task ID.

    Returns:
        dict: The result of the task.
    """
    timeout_seconds = float(os.getenv("LUMUS_API_TIMEOUT", "320.0"))
    timeout = httpx.Timeout(timeout_seconds)
    async with httpx.AsyncClient(timeout=timeout) as client:
        try:
            result = await client.get(LUMUS_URL + "/task/result/" + task_id)
            if result.status_code == 200:
                return result.json()
            else:
                return {"error": str(result.status_code) + " " + result.text}
        except Exception as e:
            return {"error": "Error calling Lumus. " + str(e)}
