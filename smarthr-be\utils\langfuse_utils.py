# utils/langfuse_utils.py

import logging
from typing import Optional
from contextlib import contextmanager

from langfuse import <PERSON><PERSON>, get_client
from langfuse.langchain import <PERSON><PERSON><PERSON><PERSON><PERSON>
from core.config import settings

logger = logging.getLogger(__name__)


class LangfuseManager:
    """
    Manager class for Langfuse integration with SmartHR application.
    
    Provides utilities for:
    - Initializing Langfuse client
    - Creating callback handlers for LangChain integration
    - Managing trace context and attributes
    - Error handling and graceful degradation
    """
    
    _client: Optional[Langfuse] = None
    _initialized: bool = False
    
    @classmethod
    def initialize(cls) -> bool:
        """
        Initialize the Langfuse client with configuration from settings.
        
        Returns:
            bool: True if initialization successful, False otherwise
        """
        if cls._initialized:
            return True
            
        try:
            # Check if required configuration is available
            if not all([
                settings.LANGFUSE_PUBLIC_KEY,
                settings.LANGFUSE_SECRET_KEY
            ]):
                logger.warning(
                    "Langfuse configuration incomplete. "
                    "LANGFUSE_PUBLIC_KEY and LANGFUSE_SECRET_KEY are required. "
                    "Langfuse tracing will be disabled."
                )
                return False
            
            # Initialize Langfuse client
            Langfuse(
                public_key=settings.LANGFUSE_PUBLIC_KEY,
                secret_key=settings.LANGFUSE_SECRET_KEY,
                host=settings.LANGFUSE_HOST
            )
            
            cls._client = get_client()
            cls._initialized = True
            
            logger.info(f"Langfuse initialized successfully with host: {settings.LANGFUSE_HOST}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Langfuse: {str(e)}")
            return False
    
    @classmethod
    def get_client(cls) -> Optional[Langfuse]:
        """
        Get the initialized Langfuse client.
        
        Returns:
            Optional[Langfuse]: The Langfuse client if initialized, None otherwise
        """
        if not cls._initialized:
            cls.initialize()
        return cls._client
    
    @classmethod
    def create_callback_handler(
        cls,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        tags: Optional[list] = None
    ) -> Optional[CallbackHandler]:
        """
        Create a Langfuse callback handler for LangChain integration.
        
        Args:
            user_id: Optional user identifier for trace attribution
            session_id: Optional session identifier for grouping traces
            tags: Optional list of tags for trace categorization
            
        Returns:
            Optional[CallbackHandler]: Callback handler if Langfuse is available, None otherwise
        """
        if not cls.initialize():
            logger.debug("Langfuse not available, returning None callback handler")
            return None
            
        try:
            handler = CallbackHandler()
            
            # Set trace attributes if provided
            if any([user_id, session_id, tags]):
                client = cls.get_client()
                if client:
                    # Create a span to set trace attributes
                    with client.start_as_current_span(name="smarthr-llm-operation") as span:
                        span.update_trace(
                            user_id=user_id,
                            session_id=session_id,
                            tags=tags or []
                        )
            
            return handler
            
        except Exception as e:
            logger.error(f"Failed to create Langfuse callback handler: {str(e)}")
            return None
    
    @classmethod
    def create_metadata_for_langchain(
        cls,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        tags: Optional[list] = None
    ) -> dict:
        """
        Create metadata dictionary for LangChain invocation with Langfuse attributes.
        
        This is an alternative approach to setting trace attributes via metadata
        instead of using spans.
        
        Args:
            user_id: Optional user identifier
            session_id: Optional session identifier  
            tags: Optional list of tags
            
        Returns:
            dict: Metadata dictionary with Langfuse attributes
        """
        metadata = {}
        
        if user_id:
            metadata["langfuse_user_id"] = user_id
        if session_id:
            metadata["langfuse_session_id"] = session_id
        if tags:
            metadata["langfuse_tags"] = tags
            
        return metadata
    
    @classmethod
    @contextmanager
    def trace_context(
        cls,
        name: str,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        tags: Optional[list] = None,
        input_data: Optional[dict] = None,
        **kwargs
    ):
        """
        Context manager for creating a Langfuse trace with automatic cleanup.
        
        Args:
            name: Name of the trace
            user_id: Optional user identifier
            session_id: Optional session identifier
            tags: Optional list of tags
            input_data: Optional input data for the trace
            **kwargs: Additional trace attributes
            
        Yields:
            span: Langfuse span object for the trace
        """
        client = cls.get_client()
        if not client:
            # If Langfuse is not available, yield a no-op context
            yield None
            return
            
        try:
            with client.start_as_current_span(name=name) as span:
                # Set trace attributes
                trace_attrs = {
                    "user_id": user_id,
                    "session_id": session_id,
                    "tags": tags or [],
                    "input": input_data,
                    **kwargs
                }
                # Filter out None values
                trace_attrs = {k: v for k, v in trace_attrs.items() if v is not None}
                
                if trace_attrs:
                    span.update_trace(**trace_attrs)
                
                yield span
                
        except Exception as e:
            logger.error(f"Error in Langfuse trace context: {str(e)}")
            yield None
    
    @classmethod
    def flush(cls) -> None:
        """
        Flush pending events to Langfuse.
        
        This should be called in short-lived applications to ensure
        all events are sent before the application exits.
        """
        client = cls.get_client()
        if client:
            try:
                client.flush()
                logger.debug("Langfuse events flushed successfully")
            except Exception as e:
                logger.error(f"Failed to flush Langfuse events: {str(e)}")
    
    @classmethod
    def shutdown(cls) -> None:
        """
        Shutdown the Langfuse client and flush remaining events.
        
        This should be called when the application is shutting down.
        """
        client = cls.get_client()
        if client:
            try:
                client.shutdown()
                logger.info("Langfuse client shutdown successfully")
            except Exception as e:
                logger.error(f"Failed to shutdown Langfuse client: {str(e)}")
        
        cls._client = None
        cls._initialized = False


# Convenience functions for easy usage
def get_langfuse_callback_handler(
    user_id: Optional[str] = None,
    session_id: Optional[str] = None,
    tags: Optional[list] = None
) -> Optional[CallbackHandler]:
    """
    Convenience function to get a Langfuse callback handler.
    
    Args:
        user_id: Optional user identifier
        session_id: Optional session identifier
        tags: Optional list of tags
        
    Returns:
        Optional[CallbackHandler]: Callback handler if available, None otherwise
    """
    return LangfuseManager.create_callback_handler(
        user_id=user_id,
        session_id=session_id,
        tags=tags
    )


def get_langfuse_metadata(
    user_id: Optional[str] = None,
    session_id: Optional[str] = None,
    tags: Optional[list] = None
) -> dict:
    """
    Convenience function to get Langfuse metadata for LangChain.
    
    Args:
        user_id: Optional user identifier
        session_id: Optional session identifier
        tags: Optional list of tags
        
    Returns:
        dict: Metadata dictionary with Langfuse attributes
    """
    return LangfuseManager.create_metadata_for_langchain(
        user_id=user_id,
        session_id=session_id,
        tags=tags
    )


# Initialize Langfuse on module import (with error handling)
try:
    LangfuseManager.initialize()
except Exception as e:
    logger.warning(f"Failed to initialize Langfuse on module import: {e}")
