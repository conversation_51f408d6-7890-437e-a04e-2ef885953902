# Langfuse Integration for SmartHR

This document explains how to use the Langfuse integration in the SmartHR application for comprehensive LLM observability and tracing.

## 🚀 Quick Start

### 1. Configuration

Set up your environment variables in `.env`:

```bash
# Langfuse Configuration
LANGFUSE_PUBLIC_KEY=pk-lf-your-public-key
LANGFUSE_SECRET_KEY=sk-lf-your-secret-key
LANGFUSE_HOST=https://cloud.langfuse.com
```

### 2. Test the Connection

Run the connection test script:

```bash
cd smarthr-be
python test_langfuse_connection.py
```

### 3. Check Your Dashboard

Visit your Langfuse dashboard at the URL specified in `LANGFUSE_HOST` to see traces.

## 📊 Automatic Tracing

**All existing LLM calls are now automatically traced!** The integration has been added to the core `inference_with_fallback` function, so every LLM operation in your application will be traced without any code changes.

### What's Automatically Traced

- ✅ Candidate evaluations (`evaluate_candidate`)
- ✅ Batch candidate processing (`evaluate_candidates_batch`)
- ✅ Interview transcript processing
- ✅ Position analysis and creation
- ✅ All `inference_with_fallback` calls
- ✅ Model fallback attempts and failures

## 🎯 Enhanced Tracing (Optional)

For more detailed tracing, you can add optional parameters to your existing function calls:

### Basic Enhancement

```python
# Before (still works, but with basic tracing)
result = inference_with_fallback(
    task_prompt="Analyze this candidate...",
    user_messages=[HumanMessage(content=candidate_text)]
)

# After (enhanced tracing)
result = inference_with_fallback(
    task_prompt="Analyze this candidate...",
    user_messages=[HumanMessage(content=candidate_text)],
    # Add these for better tracing
    user_id="recruiter-123",
    session_id="candidate-review-session",
    trace_tags=["candidate-analysis", "technical-review"],
    trace_name="candidate-technical-evaluation"
)
```

### Candidate Evaluation Enhancement

```python
# Enhanced candidate evaluation
analysis = evaluate_candidate(
    candidate=candidate_text,
    position=position_text,
    # Add tracing context
    user_id="hr-manager-456",
    session_id="hiring-session-789",
    candidate_id="candidate-123",
    position_id="position-456"
)
```

### Batch Processing Enhancement

```python
# Enhanced batch evaluation
batch_result = evaluate_candidates_batch(
    candidates=candidate_list,
    position=position_text,
    # Add tracing context
    user_id="hr-director-789",
    session_id="bulk-evaluation-session",
    position_id="position-123"
)
```

## 🔧 Advanced Usage

### Using Trace Context Managers

For complex operations spanning multiple LLM calls:

```python
from utils.langfuse_utils import LangfuseManager

with LangfuseManager.trace_context(
    name="complete-hiring-pipeline",
    user_id="hr-manager-123",
    session_id="hiring-pipeline-456",
    tags=["hiring", "pipeline", "end-to-end"],
    input_data={"candidate_id": "candidate-789", "position_id": "position-123"}
) as span:
    
    # Step 1: Candidate analysis
    analysis = evaluate_candidate(...)
    
    # Step 2: Interview processing
    interview_result = process_interview(...)
    
    # Step 3: Final recommendation
    recommendation = generate_recommendation(...)
    
    # Update trace with final results
    if span:
        span.update_trace(
            output={
                "recommendation": recommendation,
                "final_score": analysis.get("Score"),
                "pipeline_completed": True
            }
        )
```

### Session-Based Tracing

Group related operations under the same session:

```python
session_id = "candidate-review-session-123"
user_id = "recruiter-456"

# All these operations will be grouped under the same session
candidate_analysis = evaluate_candidate(..., session_id=session_id, user_id=user_id)
interview_evaluation = process_interview(..., session_id=session_id, user_id=user_id)
final_decision = make_hiring_decision(..., session_id=session_id, user_id=user_id)
```

## 📈 What You'll See in Langfuse

### Trace Information

- **Trace Names**: Descriptive names like "candidate-evaluation", "batch-processing"
- **User Attribution**: Track which HR manager or recruiter made the request
- **Session Grouping**: Related operations grouped together
- **Tags**: Categorize traces by type (candidate-analysis, interview, etc.)

### Performance Metrics

- **Model Performance**: See which models (GPT-4, Llama, etc.) perform best
- **Response Times**: Track LLM response latencies
- **Failure Rates**: Monitor model fallback patterns
- **Token Usage**: Track costs and usage patterns

### Business Context

- **Candidate IDs**: Link traces to specific candidates
- **Position IDs**: Track which positions are being analyzed
- **Scores and Results**: See evaluation scores and outcomes
- **Error Tracking**: Monitor and debug LLM failures

## 🛠️ Examples and Testing

### Run Examples

```bash
# Basic integration examples
python examples/langfuse_integration_examples.py

# Enhanced controller examples
python examples/controller_tracing_examples.py
```

### Health Check

Check the integration status:

```bash
curl http://localhost:8080/health/langfuse
```

Or add the health router to your main.py:

```python
from routes.routes_health import router as health_router
app.include_router(health_router, prefix="/health", tags=["health"])
```

## 🔍 Troubleshooting

### Common Issues

1. **No traces appearing**: Check your API keys and network connectivity
2. **Traces not grouped**: Ensure you're using the same `session_id` for related operations
3. **Missing context**: Add `user_id` and `trace_tags` for better categorization

### Debug Mode

Enable debug logging to see Langfuse operations:

```python
import logging
logging.getLogger("langfuse").setLevel(logging.DEBUG)
```

### Manual Flush

In short-lived scripts, manually flush events:

```python
from utils.langfuse_utils import LangfuseManager
LangfuseManager.flush()
```

## 🎨 Best Practices

### 1. Consistent Naming

Use consistent trace names and tags:

```python
# Good
trace_name="candidate-evaluation"
trace_tags=["candidate", "evaluation", "technical"]

# Avoid
trace_name="eval_cand_123"
trace_tags=["misc", "stuff"]
```

### 2. Meaningful Context

Include business context in your traces:

```python
# Good
user_id="recruiter-sarah-jones"
session_id="senior-dev-hiring-q4-2024"
trace_tags=["senior-developer", "technical-interview", "q4-hiring"]

# Basic
user_id="user123"
session_id="session456"
```

### 3. Error Handling

The integration includes graceful error handling - if Langfuse is unavailable, your LLM calls will still work normally.

### 4. Performance

Langfuse uses background processing, so it won't slow down your LLM calls. The overhead is minimal.

## 📚 Additional Resources

- [Langfuse Documentation](https://langfuse.com/docs)
- [LangChain Integration Guide](https://langfuse.com/docs/integrations/langchain)
- [SmartHR Examples](./examples/)

## 🤝 Support

If you encounter issues with the Langfuse integration:

1. Check the connection test: `python test_langfuse_connection.py`
2. Verify your environment variables
3. Check the health endpoint: `/health/langfuse`
4. Review the examples in the `examples/` directory

The integration is designed to be robust - if Langfuse is unavailable, your application will continue to work normally without tracing.
