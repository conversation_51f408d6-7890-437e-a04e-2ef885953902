#llm.py

import os
from typing import List, Optional, get_origin, get_args
from dotenv import load_dotenv
import inspect
from pydantic import BaseModel

# LangChain / LLM imports
from langchain_community.cache import InMemoryCache
from langchain.globals import set_llm_cache
from langchain_core.messages import HumanMessage, SystemMessage

# Custom wrappers (assuming these are your wrappers)
from langchain_openai import AzureChatOpenAI
from langchain_groq import ChatGroq

# Langfuse integration - imported inside function to avoid circular imports

load_dotenv()  # Load environment variables
GROQ_API_KEY = os.getenv("GROQ_API_KEY").strip()

class LLM_Model:
    """Class representing LLM models."""
    def __init__(self, name: str, provider: str = "openai", temperature: float = 0):
        if provider == "openai":
            self.llm = AzureChatOpenAI(
                temperature=temperature,
                deployment_name=name
            )
        elif provider == "grok":
            self.llm = ChatGroq(
                temperature=temperature,
                model=name,
                api_key=GROQ_API_KEY
            )

        else:
            raise ValueError(f"Unsupported provider: {provider}")

    def get_llm(self):
        return self.llm


# Set up the LangChain in-memory cache globally
set_llm_cache(InMemoryCache())

# Instantiate your models
models_pool = {
    "gpt-4o": LLM_Model("gpt-4o-smarthr", provider="openai").get_llm(),
    "gpt-4o-mini": LLM_Model("gpt-4o-mini-smarthr", provider="openai").get_llm(),
    "llama33-70b": LLM_Model("llama-3.3-70b-versatile", provider="grok").get_llm(),
    "llama4-light": LLM_Model("meta-llama/llama-4-scout-17b-16e-instruct", provider="grok").get_llm(),
    "llama4-pro": LLM_Model("meta-llama/llama-4-maverick-17b-128e-instruct", provider="grok").get_llm(),
    "llama32-90b": LLM_Model("llama-3.2-90b-vision-preview", provider="grok").get_llm(),
}

def get_related_class_definitions(cls, visited=None):
    if visited is None:
        visited = set()
    if cls in visited:
        return ""
    
    visited.add(cls)
    source_code = inspect.getsource(cls)
    
    for field in cls.__annotations__.values():
        origin = get_origin(field)
        if origin in [list, List, Optional]:  # Check for generics
            inner_type = get_args(field)[0]
            if isinstance(inner_type, type) and issubclass(inner_type, BaseModel) and inner_type not in visited:
                source_code += "\n\n" + get_related_class_definitions(inner_type, visited)
        elif isinstance(field, type) and issubclass(field, BaseModel) and field not in visited:
            source_code += "\n\n" + get_related_class_definitions(field, visited)
    
    return source_code

def inference_with_fallback(
    task_prompt: Optional[str] = None,
    model_schema=None,                  # Possibly a Pydantic class or other schema class
    user_messages: Optional[List] = None,  # Should be a list of HumanMessage objects
    model_schema_text: Optional[str] = None, # Posible POydantic class in string format
    models_order: Optional[List[str]] = None,
    # Langfuse tracing parameters
    user_id: Optional[str] = None,
    session_id: Optional[str] = None,
    trace_tags: Optional[List[str]] = None,
    trace_name: Optional[str] = None
):
    """
    Attempt to invoke inference on each model in `models_order`, returning
    as soon as one model succeeds. If all fail, return None or raise an exception.
    For invoke the function, in case of success there are two kinds of invocation:
    1. If the model returns a structured output (like JSON), it will should be transformed using model_dump
    2. If the model returns a non structured output, it will be used as is as the response.content to extract the response in string format

    :param task_prompt: The main instruction to be used in a system message.
    :param model_schema: A class or schema definition for structured output (optional).
    :param user_messages: A list of HumanMessage objects.
    :param model_schema_text: Additional textual instructions describing the JSON schema.
    :param models_order: The order in which to try the models.
    :param user_id: Optional user identifier for Langfuse tracing.
    :param session_id: Optional session identifier for Langfuse tracing.
    :param trace_tags: Optional list of tags for Langfuse tracing.
    :param trace_name: Optional name for the Langfuse trace.
    :return: The model response (string or parsed object) or None if all fail.
    """
    if models_order is None:
        models_order = ["llama4-pro", "llama4-light", "gpt-4o","gpt-4o-mini"]

    if not task_prompt:
        print("No system prompt found. Returning None.")
        return None

    # Set up Langfuse tracing (import here to avoid circular imports)
    try:
        from utils.langfuse_utils import get_langfuse_callback_handler, get_langfuse_metadata

        langfuse_handler = get_langfuse_callback_handler(
            user_id=user_id,
            session_id=session_id,
            tags=trace_tags
        )

        # Create callbacks list
        callbacks = [langfuse_handler] if langfuse_handler else []

        # Create metadata for alternative tracing approach
        langfuse_metadata = get_langfuse_metadata(
            user_id=user_id,
            session_id=session_id,
            tags=trace_tags
        )
    except ImportError as e:
        print(f"Langfuse not available: {e}")
        callbacks = []
        langfuse_metadata = {}

    # Determine trace name
    if not trace_name:
        if model_schema:
            trace_name = f"llm-inference-{model_schema.__name__}"
        else:
            trace_name = "llm-inference"

    if not user_messages or len(user_messages) == 0:
        print("No user messages found. Returning None.")
        return None

    # Create the system message content
    if model_schema is None:
        # If there's no model schema, we just pass the prompt
        print("No model schema found. Using minimal system prompt.")
        system_prompt = f"""
        [PRIORITY TASK]
        Your priority is following the next guideline:
        {task_prompt}
        """
        system_message = SystemMessage(content=system_prompt)
        use_schema = False
    else:
        # If there's a model schema, assume we want structured output
        system_prompt = f"""
        [PRIORITY TASK]
        {task_prompt}
        [JSON Expected Output Format]
        {model_schema_text}
        """
        system_message = SystemMessage(content=system_prompt)
        use_schema = True

    # Try models in order
    retry = 2
    for model_name in models_order:
        while retry > 0:
            try:
                if model_name not in models_pool:
                    print(f"Model '{model_name}' not found in models_pool. Skipping.")
                    continue

                model = models_pool[model_name]

                # If a schema is specified, we assume the model has a `.with_structured_output(...)` method
                # This might be a custom method you wrote in your wrappers.  
                # If not, you’ll need a different approach (like using a PydanticOutputParser).
                if use_schema:
                    model = model.with_structured_output(model_schema, method="json_mode")

                # Combine system message and user messages
                messages = [system_message] + user_messages

                # Create config with callbacks and metadata for Langfuse tracing
                config = {}
                if callbacks:
                    config["callbacks"] = callbacks
                if langfuse_metadata:
                    config["metadata"] = langfuse_metadata

                # Set run name for better trace identification
                if trace_name:
                    config["run_name"] = f"{trace_name}-{model_name}"

                # Actually call the model with Langfuse tracing
                if config:
                    result = model.invoke(messages, config=config)
                else:
                    result = model.invoke(messages)

                # If successful, return result and exit
                # Use .model_dump or .content 
                return result

            except Exception as e:
                # Keep trying the next model
                print(f"Error with model '{model_name}': {e}")
            retry = retry-1
        retry = 2
    # If we reach here, all models failed
    print("All models in models_order failed. Returning None.")
    return None
