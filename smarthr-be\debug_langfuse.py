#!/usr/bin/env python3
"""
Script de diagnóstico detallado para Langfuse.
Este script ayuda a identificar exactamente por qué las trazas no se están guardando.
"""

import os
import sys
import time
import traceback
from dotenv import load_dotenv

# Cargar variables de entorno
load_dotenv()

def check_environment():
    """Verificar variables de entorno"""
    print("🔧 Verificando Variables de Entorno...")
    
    required_vars = {
        'LANGFUSE_PUBLIC_KEY': os.getenv('LANGFUSE_PUBLIC_KEY'),
        'LANGFUSE_SECRET_KEY': os.getenv('LANGFUSE_SECRET_KEY'),
        'LANGFUSE_HOST': os.getenv('LANGFUSE_HOST', 'https://cloud.langfuse.com')
    }
    
    all_good = True
    for var, value in required_vars.items():
        if value:
            # Mostrar solo los primeros y últimos caracteres por seguridad
            if 'KEY' in var and len(value) > 10:
                display_value = f"{value[:8]}...{value[-4:]}"
            else:
                display_value = value
            print(f"  ✅ {var}: {display_value}")
        else:
            print(f"  ❌ {var}: NO CONFIGURADA")
            all_good = False
    
    return all_good, required_vars

def test_langfuse_import():
    """Probar importación de Langfuse"""
    print("\n📦 Probando Importación de Langfuse...")
    
    try:
        import langfuse
        print(f"  ✅ langfuse importado correctamente (versión: {langfuse.__version__ if hasattr(langfuse, '__version__') else 'desconocida'})")
        return True
    except ImportError as e:
        print(f"  ❌ Error importando langfuse: {e}")
        print("  💡 Ejecuta: pip install langfuse")
        return False

def test_langfuse_client():
    """Probar inicialización del cliente Langfuse"""
    print("\n🚀 Probando Cliente Langfuse...")
    
    try:
        from langfuse import Langfuse
        
        client = Langfuse(
            public_key=os.getenv('LANGFUSE_PUBLIC_KEY'),
            secret_key=os.getenv('LANGFUSE_SECRET_KEY'),
            host=os.getenv('LANGFUSE_HOST', 'https://cloud.langfuse.com')
        )
        
        print("  ✅ Cliente Langfuse inicializado")
        return True, client
    except Exception as e:
        print(f"  ❌ Error inicializando cliente: {e}")
        print(f"  🔍 Traceback: {traceback.format_exc()}")
        return False, None

def test_simple_trace(client):
    """Probar creación de traza simple"""
    print("\n📊 Probando Creación de Traza Simple...")
    
    try:
        # Crear una traza simple
        trace = client.trace(
            name="test-trace-debug",
            input={"test": "debug trace"},
            metadata={"source": "debug_script"}
        )
        
        print(f"  ✅ Traza creada con ID: {trace.id}")
        
        # Agregar un span
        span = trace.span(
            name="test-span",
            input={"operation": "test"},
            output={"result": "success"}
        )
        
        print(f"  ✅ Span creado con ID: {span.id}")
        
        # Finalizar la traza
        trace.update(output={"status": "completed"})
        
        return True, trace.id
    except Exception as e:
        print(f"  ❌ Error creando traza: {e}")
        print(f"  🔍 Traceback: {traceback.format_exc()}")
        return False, None

def test_langchain_callback():
    """Probar callback de LangChain"""
    print("\n🔗 Probando Callback de LangChain...")
    
    try:
        from langfuse.langchain import CallbackHandler
        
        handler = CallbackHandler()
        print("  ✅ CallbackHandler creado")
        
        # Probar con un mensaje simple
        from langchain_core.messages import HumanMessage
        
        messages = [HumanMessage(content="Test message")]
        print("  ✅ Mensajes de prueba creados")
        
        return True, handler
    except Exception as e:
        print(f"  ❌ Error con callback: {e}")
        print(f"  🔍 Traceback: {traceback.format_exc()}")
        return False, None

def test_utils_import():
    """Probar importación de utils"""
    print("\n🛠️ Probando Importación de Utils...")
    
    try:
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from utils.langfuse_utils import LangfuseManager, get_langfuse_callback_handler
        
        print("  ✅ Utils importados correctamente")
        
        # Probar inicialización
        success = LangfuseManager.initialize()
        print(f"  {'✅' if success else '❌'} LangfuseManager.initialize(): {success}")
        
        # Probar callback handler
        handler = get_langfuse_callback_handler(
            user_id="debug-user",
            session_id="debug-session",
            tags=["debug", "test"]
        )
        print(f"  {'✅' if handler else '❌'} get_langfuse_callback_handler(): {'OK' if handler else 'None'}")
        
        return True, handler
    except Exception as e:
        print(f"  ❌ Error con utils: {e}")
        print(f"  🔍 Traceback: {traceback.format_exc()}")
        return False, None

def test_llm_integration():
    """Probar integración con LLM"""
    print("\n🤖 Probando Integración con LLM...")
    
    try:
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from models.llm import inference_with_fallback
        from langchain_core.messages import HumanMessage
        
        print("  ✅ Funciones LLM importadas")
        
        # Probar con parámetros de tracing
        result = inference_with_fallback(
            task_prompt="Responde con 'OK' si recibes este mensaje.",
            user_messages=[HumanMessage(content="Test message")],
            models_order=["gpt-4o-mini"],  # Usar solo un modelo para la prueba
            user_id="debug-user-llm",
            session_id="debug-session-llm",
            trace_tags=["debug", "llm-test"],
            trace_name="debug-llm-call"
        )
        
        print(f"  ✅ LLM llamado exitosamente")
        print(f"  📝 Resultado: {str(result)[:100]}...")
        
        return True, result
    except Exception as e:
        print(f"  ❌ Error con LLM: {e}")
        print(f"  🔍 Traceback: {traceback.format_exc()}")
        return False, None

def test_flush_and_wait(client):
    """Probar flush y esperar"""
    print("\n⏳ Probando Flush y Espera...")
    
    try:
        print("  🔄 Ejecutando flush...")
        client.flush()
        
        print("  ⏱️ Esperando 5 segundos para que se procesen las trazas...")
        time.sleep(5)
        
        print("  ✅ Flush completado")
        return True
    except Exception as e:
        print(f"  ❌ Error en flush: {e}")
        return False

def main():
    """Función principal de diagnóstico"""
    print("🔍 DIAGNÓSTICO DETALLADO DE LANGFUSE")
    print("=" * 50)
    
    # 1. Verificar variables de entorno
    env_ok, env_vars = check_environment()
    if not env_ok:
        print("\n❌ PROBLEMA: Variables de entorno no configuradas correctamente")
        print("💡 SOLUCIÓN: Configura las variables en tu archivo .env")
        return False
    
    # 2. Verificar importación
    if not test_langfuse_import():
        print("\n❌ PROBLEMA: Langfuse no está instalado")
        print("💡 SOLUCIÓN: pip install langfuse")
        return False
    
    # 3. Probar cliente
    client_ok, client = test_langfuse_client()
    if not client_ok:
        print("\n❌ PROBLEMA: No se puede inicializar el cliente Langfuse")
        print("💡 SOLUCIÓN: Verifica tus credenciales y conectividad")
        return False
    
    # 4. Probar traza simple
    trace_ok, trace_id = test_simple_trace(client)
    if not trace_ok:
        print("\n❌ PROBLEMA: No se pueden crear trazas")
        return False
    
    # 5. Probar callback de LangChain
    callback_ok, handler = test_langchain_callback()
    if not callback_ok:
        print("\n❌ PROBLEMA: Callback de LangChain no funciona")
        return False
    
    # 6. Probar utils
    utils_ok, utils_handler = test_utils_import()
    if not utils_ok:
        print("\n❌ PROBLEMA: Utils de Langfuse no funcionan")
        return False
    
    # 7. Probar integración LLM
    llm_ok, llm_result = test_llm_integration()
    if not llm_ok:
        print("\n❌ PROBLEMA: Integración con LLM no funciona")
        return False
    
    # 8. Flush y esperar
    flush_ok = test_flush_and_wait(client)
    
    # Resumen final
    print("\n" + "=" * 50)
    print("📋 RESUMEN DEL DIAGNÓSTICO")
    print("=" * 50)
    
    checks = [
        ("Variables de entorno", env_ok),
        ("Importación Langfuse", True),
        ("Cliente Langfuse", client_ok),
        ("Creación de trazas", trace_ok),
        ("Callback LangChain", callback_ok),
        ("Utils Langfuse", utils_ok),
        ("Integración LLM", llm_ok),
        ("Flush", flush_ok)
    ]
    
    all_passed = all(check[1] for check in checks)
    
    for check_name, passed in checks:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"  {check_name}: {status}")
    
    if all_passed:
        print(f"\n🎉 ¡TODO FUNCIONA CORRECTAMENTE!")
        print(f"📊 Traza de prueba creada: {trace_id}")
        print(f"🌐 Revisa tu dashboard en: {env_vars['LANGFUSE_HOST']}")
        print(f"⏰ Las trazas pueden tardar unos minutos en aparecer")
    else:
        print(f"\n⚠️ ALGUNOS PROBLEMAS DETECTADOS")
        print(f"💡 Revisa los errores arriba para más detalles")
    
    return all_passed

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️ Diagnóstico interrumpido por el usuario")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n💥 Error inesperado: {e}")
        print(f"🔍 Traceback completo: {traceback.format_exc()}")
        sys.exit(1)
