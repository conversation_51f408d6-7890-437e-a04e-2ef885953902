#!/usr/bin/env python3
"""
Prueba simple y directa de Langfuse con una llamada LLM real.
Este script hace una llamada LLM mínima con tracing para verificar que funciona.
"""

import os
import sys
import time
from dotenv import load_dotenv

# Cargar variables de entorno
load_dotenv()

def test_direct_langfuse():
    """Prueba directa con Langfuse sin usar nuestros utils"""
    print("🧪 Prueba Directa con Langfuse")
    print("-" * 40)
    
    try:
        from langfuse import Langfuse
        from langfuse.langchain import CallbackHandler
        
        # Inicializar cliente
        langfuse = Langfuse(
            public_key=os.getenv('LANGFUSE_PUBLIC_KEY'),
            secret_key=os.getenv('LANGFUSE_SECRET_KEY'),
            host=os.getenv('LANGFUSE_HOST', 'https://cloud.langfuse.com')
        )
        
        print("✅ Cliente Langfuse inicializado")
        
        # Crear callback handler
        handler = CallbackHandler()
        print("✅ CallbackHandler creado")
        
        # Probar con un modelo simple
        from langchain_openai import AzureChatOpenAI
        from langchain_core.messages import HumanMessage
        
        # Usar tu configuración de Azure OpenAI
        llm = AzureChatOpenAI(
            deployment_name="gpt-4o-mini-smarthr",  # Ajusta según tu deployment
            temperature=0
        )
        
        print("✅ Modelo LLM inicializado")
        
        # Hacer una llamada simple con tracing
        messages = [HumanMessage(content="Responde solo con 'FUNCIONA' si recibes este mensaje.")]
        
        print("🚀 Haciendo llamada LLM con tracing...")
        
        result = llm.invoke(
            messages, 
            config={
                "callbacks": [handler],
                "run_name": "test-simple-trace",
                "metadata": {
                    "test": "simple_trace",
                    "user": "debug-user"
                }
            }
        )
        
        print(f"✅ LLM respondió: {result.content}")
        
        # Flush para asegurar que se envíe
        print("📤 Enviando traza a Langfuse...")
        langfuse.flush()
        
        print("⏳ Esperando 3 segundos...")
        time.sleep(3)
        
        print("✅ Prueba directa completada")
        return True
        
    except Exception as e:
        print(f"❌ Error en prueba directa: {e}")
        import traceback
        print(f"🔍 Traceback: {traceback.format_exc()}")
        return False

def test_with_our_utils():
    """Prueba usando nuestros utils"""
    print("\n🛠️ Prueba con Nuestros Utils")
    print("-" * 40)
    
    try:
        # Agregar el directorio actual al path
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from utils.langfuse_utils import LangfuseManager, get_langfuse_callback_handler
        
        # Inicializar
        success = LangfuseManager.initialize()
        print(f"{'✅' if success else '❌'} LangfuseManager inicializado: {success}")
        
        if not success:
            return False
        
        # Obtener callback handler
        handler = get_langfuse_callback_handler(
            user_id="test-user-utils",
            session_id="test-session-utils",
            tags=["test", "utils", "debug"]
        )
        
        print(f"{'✅' if handler else '❌'} Callback handler obtenido: {'OK' if handler else 'None'}")
        
        if not handler:
            return False
        
        # Probar con modelo
        from langchain_openai import AzureChatOpenAI
        from langchain_core.messages import HumanMessage
        
        llm = AzureChatOpenAI(
            deployment_name="gpt-4o-mini-smarthr",
            temperature=0
        )
        
        messages = [HumanMessage(content="Responde solo con 'UTILS FUNCIONA' si recibes este mensaje.")]
        
        print("🚀 Haciendo llamada LLM con nuestros utils...")
        
        result = llm.invoke(
            messages,
            config={
                "callbacks": [handler],
                "run_name": "test-utils-trace"
            }
        )
        
        print(f"✅ LLM respondió: {result.content}")
        
        # Flush
        LangfuseManager.flush()
        
        print("✅ Prueba con utils completada")
        return True
        
    except Exception as e:
        print(f"❌ Error con utils: {e}")
        import traceback
        print(f"🔍 Traceback: {traceback.format_exc()}")
        return False

def test_inference_with_fallback():
    """Prueba con inference_with_fallback"""
    print("\n🤖 Prueba con inference_with_fallback")
    print("-" * 40)
    
    try:
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from models.llm import inference_with_fallback
        from langchain_core.messages import HumanMessage
        
        print("✅ inference_with_fallback importado")
        
        result = inference_with_fallback(
            task_prompt="Eres un asistente útil. Responde brevemente.",
            user_messages=[HumanMessage(content="Responde solo con 'INFERENCE FUNCIONA' si recibes este mensaje.")],
            models_order=["gpt-4o-mini"],
            user_id="test-user-inference",
            session_id="test-session-inference",
            trace_tags=["test", "inference", "debug"],
            trace_name="test-inference-trace"
        )
        
        print(f"✅ inference_with_fallback respondió: {result.content if hasattr(result, 'content') else str(result)[:100]}")
        
        # Flush usando utils
        from utils.langfuse_utils import LangfuseManager
        LangfuseManager.flush()
        
        print("✅ Prueba con inference_with_fallback completada")
        return True
        
    except Exception as e:
        print(f"❌ Error con inference_with_fallback: {e}")
        import traceback
        print(f"🔍 Traceback: {traceback.format_exc()}")
        return False

def main():
    """Función principal"""
    print("🔬 PRUEBA SIMPLE DE LANGFUSE TRACING")
    print("=" * 50)
    
    # Verificar variables de entorno
    required_vars = ['LANGFUSE_PUBLIC_KEY', 'LANGFUSE_SECRET_KEY']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"❌ Variables faltantes: {', '.join(missing_vars)}")
        print("💡 Configura estas variables en tu archivo .env")
        return False
    
    print("✅ Variables de entorno configuradas")
    
    # Ejecutar pruebas
    tests = [
        ("Prueba Directa", test_direct_langfuse),
        ("Prueba con Utils", test_with_our_utils),
        ("Prueba inference_with_fallback", test_inference_with_fallback)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} falló: {e}")
            results.append((test_name, False))
    
    # Resumen
    print("\n" + "=" * 50)
    print("📋 RESUMEN DE PRUEBAS")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Resultado: {passed}/{len(results)} pruebas pasaron")
    
    if passed > 0:
        print(f"\n🎉 ¡Al menos una prueba funcionó!")
        print(f"🌐 Revisa tu dashboard en: {os.getenv('LANGFUSE_HOST', 'https://cloud.langfuse.com')}")
        print(f"⏰ Las trazas pueden tardar 1-2 minutos en aparecer")
        print(f"🔍 Busca trazas con nombres: 'test-simple-trace', 'test-utils-trace', 'test-inference-trace'")
    else:
        print(f"\n⚠️ Ninguna prueba funcionó. Ejecuta debug_langfuse.py para más detalles.")
    
    return passed > 0

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️ Prueba interrumpida por el usuario")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n💥 Error inesperado: {e}")
        import traceback
        print(f"🔍 Traceback: {traceback.format_exc()}")
        sys.exit(1)
