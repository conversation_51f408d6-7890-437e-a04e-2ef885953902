# routes/routes_health.py

from fastapi import APIRouter
from utils.langfuse_utils import LangfuseManager
from core.config import settings

router = APIRouter()

@router.get("/health/langfuse")
def check_langfuse_health():
    """
    Health check endpoint for Langfuse integration.
    Returns the status of Langfuse connection and configuration.
    """
    
    # Check if configuration is available
    config_status = {
        "public_key_set": bool(settings.LANGFUSE_PUBLIC_KEY),
        "secret_key_set": bool(settings.LANGFUSE_SECRET_KEY),
        "host": settings.LANGFUSE_HOST
    }
    
    # Check if client is initialized
    client = LangfuseManager.get_client()
    client_status = {
        "initialized": client is not None,
        "client_available": LangfuseManager._initialized
    }
    
    # Overall status
    is_healthy = (
        config_status["public_key_set"] and 
        config_status["secret_key_set"] and 
        client_status["initialized"]
    )
    
    return {
        "status": "healthy" if is_healthy else "unhealthy",
        "langfuse": {
            "configuration": config_status,
            "client": client_status,
            "ready": is_healthy
        }
    }
