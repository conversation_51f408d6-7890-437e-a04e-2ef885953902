#!/usr/bin/env python3
"""
Script para diagnosticar y solucionar problemas con las trazas de Langfuse.
Este script te guiará paso a paso para identificar y resolver el problema.
"""

import os
import sys
import time
from dotenv import load_dotenv

load_dotenv()

def step1_check_config():
    """Paso 1: Verificar configuración"""
    print("🔧 PASO 1: Verificar Configuración")
    print("-" * 40)
    
    # Verificar archivo .env
    env_file = ".env"
    if not os.path.exists(env_file):
        print(f"❌ No se encuentra el archivo {env_file}")
        print(f"💡 SOLUCIÓN: Crea un archivo .env con:")
        print(f"   LANGFUSE_PUBLIC_KEY=pk-lf-tu-clave-publica")
        print(f"   LANGFUSE_SECRET_KEY=sk-lf-tu-clave-secreta")
        print(f"   LANGFUSE_HOST=https://cloud.langfuse.com")
        return False
    
    print(f"✅ Archivo {env_file} encontrado")
    
    # Verificar variables
    vars_to_check = {
        'LANGFUSE_PUBLIC_KEY': 'pk-lf-',
        'LANGFUSE_SECRET_KEY': 'sk-lf-',
        'LANGFUSE_HOST': 'https://'
    }
    
    all_good = True
    for var, prefix in vars_to_check.items():
        value = os.getenv(var)
        if not value:
            print(f"❌ {var} no está configurada")
            all_good = False
        elif not value.startswith(prefix):
            print(f"⚠️ {var} no tiene el formato esperado (debería empezar con '{prefix}')")
            all_good = False
        else:
            print(f"✅ {var} configurada correctamente")
    
    if not all_good:
        print(f"\n💡 SOLUCIÓN: Verifica tus credenciales en Langfuse:")
        print(f"   1. Ve a tu dashboard de Langfuse")
        print(f"   2. Ve a Settings > API Keys")
        print(f"   3. Copia las claves correctas a tu archivo .env")
    
    return all_good

def step2_test_connection():
    """Paso 2: Probar conexión"""
    print(f"\n🌐 PASO 2: Probar Conexión a Langfuse")
    print("-" * 40)
    
    try:
        from langfuse import Langfuse
        
        langfuse = Langfuse(
            public_key=os.getenv('LANGFUSE_PUBLIC_KEY'),
            secret_key=os.getenv('LANGFUSE_SECRET_KEY'),
            host=os.getenv('LANGFUSE_HOST', 'https://cloud.langfuse.com')
        )
        
        print("✅ Cliente Langfuse inicializado")
        
        # Probar creando una traza simple
        trace = langfuse.trace(
            name="connection-test",
            input={"test": "connection"},
            metadata={"source": "fix_script"}
        )
        
        trace.update(output={"status": "connected"})
        
        print("✅ Traza de prueba creada")
        
        # Flush inmediatamente
        langfuse.flush()
        print("✅ Traza enviada a Langfuse")
        
        return True, trace.id
        
    except Exception as e:
        print(f"❌ Error de conexión: {e}")
        
        if "401" in str(e) or "Unauthorized" in str(e):
            print(f"💡 SOLUCIÓN: Credenciales incorrectas")
            print(f"   - Verifica tu LANGFUSE_PUBLIC_KEY y LANGFUSE_SECRET_KEY")
        elif "timeout" in str(e).lower() or "connection" in str(e).lower():
            print(f"💡 SOLUCIÓN: Problema de conectividad")
            print(f"   - Verifica tu conexión a internet")
            print(f"   - Verifica que LANGFUSE_HOST sea correcto")
        else:
            print(f"💡 SOLUCIÓN: Error desconocido")
            print(f"   - Verifica que langfuse esté instalado: pip install langfuse")
        
        return False, None

def step3_test_langchain():
    """Paso 3: Probar integración LangChain"""
    print(f"\n🔗 PASO 3: Probar Integración LangChain")
    print("-" * 40)
    
    try:
        from langfuse.langchain import CallbackHandler
        from langfuse import Langfuse
        
        # Inicializar
        langfuse = Langfuse(
            public_key=os.getenv('LANGFUSE_PUBLIC_KEY'),
            secret_key=os.getenv('LANGFUSE_SECRET_KEY'),
            host=os.getenv('LANGFUSE_HOST', 'https://cloud.langfuse.com')
        )
        
        handler = CallbackHandler()
        print("✅ CallbackHandler creado")
        
        # Verificar si tenemos Azure OpenAI configurado
        if not os.getenv('AZURE_OPENAI_API_KEY'):
            print("⚠️ Azure OpenAI no configurado, saltando prueba LLM")
            return True
        
        from langchain_openai import AzureChatOpenAI
        from langchain_core.messages import HumanMessage
        
        llm = AzureChatOpenAI(
            deployment_name="gpt-4o-mini-smarthr",
            temperature=0
        )
        
        print("✅ Modelo LLM inicializado")
        
        # Hacer llamada con tracing
        result = llm.invoke(
            [HumanMessage(content="Responde solo con 'OK' si recibes esto.")],
            config={
                "callbacks": [handler],
                "run_name": "langchain-test"
            }
        )
        
        print(f"✅ LLM respondió: {result.content}")
        
        # Flush
        langfuse.flush()
        print("✅ Traza LangChain enviada")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en integración LangChain: {e}")
        
        if "deployment" in str(e).lower():
            print(f"💡 SOLUCIÓN: Problema con deployment de Azure OpenAI")
            print(f"   - Verifica que 'gpt-4o-mini-smarthr' sea el nombre correcto")
            print(f"   - O cambia el deployment_name en el código")
        else:
            print(f"💡 SOLUCIÓN: Verifica configuración de Azure OpenAI")
        
        return False

def step4_test_our_integration():
    """Paso 4: Probar nuestra integración"""
    print(f"\n🛠️ PASO 4: Probar Nuestra Integración")
    print("-" * 40)
    
    try:
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from models.llm import inference_with_fallback
        from langchain_core.messages import HumanMessage
        
        print("✅ inference_with_fallback importado")
        
        # Hacer llamada con tracing
        result = inference_with_fallback(
            task_prompt="Eres un asistente útil. Responde brevemente.",
            user_messages=[HumanMessage(content="Responde solo con 'INTEGRACIÓN OK' si recibes esto.")],
            models_order=["gpt-4o-mini"],
            user_id="test-user",
            session_id="test-session",
            trace_tags=["test", "integration"],
            trace_name="integration-test"
        )
        
        print(f"✅ inference_with_fallback funcionó")
        print(f"📝 Resultado: {result.content if hasattr(result, 'content') else str(result)[:50]}...")
        
        # Flush usando nuestros utils
        from utils.langfuse_utils import LangfuseManager
        LangfuseManager.flush()
        print("✅ Traza de integración enviada")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en nuestra integración: {e}")
        import traceback
        print(f"🔍 Traceback: {traceback.format_exc()}")
        return False

def step5_wait_and_verify():
    """Paso 5: Esperar y verificar"""
    print(f"\n⏳ PASO 5: Esperar y Verificar")
    print("-" * 40)
    
    print("🕐 Esperando 10 segundos para que se procesen las trazas...")
    for i in range(10, 0, -1):
        print(f"   ⏱️ {i} segundos...", end="\r")
        time.sleep(1)
    
    print("\n✅ Tiempo de espera completado")
    
    dashboard_url = os.getenv('LANGFUSE_HOST', 'https://cloud.langfuse.com')
    
    print(f"\n🔍 AHORA VERIFICA EN TU DASHBOARD:")
    print(f"   1. Ve a: {dashboard_url}")
    print(f"   2. Busca trazas con nombres:")
    print(f"      - connection-test")
    print(f"      - langchain-test")
    print(f"      - integration-test")
    print(f"   3. Deberían aparecer en los últimos minutos")
    
    return True

def main():
    """Función principal"""
    print("🔧 SOLUCIONADOR DE PROBLEMAS LANGFUSE")
    print("=" * 50)
    print("Este script te ayudará a identificar por qué no se guardan las trazas")
    print("=" * 50)
    
    steps = [
        ("Verificar Configuración", step1_check_config),
        ("Probar Conexión", step2_test_connection),
        ("Probar LangChain", step3_test_langchain),
        ("Probar Integración", step4_test_our_integration),
        ("Esperar y Verificar", step5_wait_and_verify)
    ]
    
    results = []
    
    for step_name, step_func in steps:
        try:
            if step_func == step2_test_connection:
                result, trace_id = step_func()
                results.append((step_name, result))
                if result:
                    print(f"📊 ID de traza de prueba: {trace_id}")
            else:
                result = step_func()
                results.append((step_name, result))
            
            if not result and step_name != "Esperar y Verificar":
                print(f"\n⚠️ {step_name} falló. Soluciona este problema antes de continuar.")
                break
                
        except Exception as e:
            print(f"❌ Error en {step_name}: {e}")
            results.append((step_name, False))
            break
    
    # Resumen final
    print(f"\n" + "=" * 50)
    print("📋 RESUMEN DE DIAGNÓSTICO")
    print("=" * 50)
    
    for step_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {step_name}: {status}")
    
    passed = sum(1 for _, result in results if result)
    
    if passed == len(steps):
        print(f"\n🎉 ¡TODOS LOS PASOS COMPLETADOS!")
        print(f"   Si aún no ves las trazas:")
        print(f"   1. Espera 2-3 minutos más")
        print(f"   2. Refresca tu dashboard")
        print(f"   3. Verifica que estés en el proyecto correcto")
    else:
        print(f"\n⚠️ ALGUNOS PASOS FALLARON")
        print(f"   Soluciona los problemas marcados arriba")
        print(f"   Luego ejecuta este script de nuevo")
    
    print(f"\n💡 COMANDOS ÚTILES:")
    print(f"   python debug_langfuse.py     # Diagnóstico detallado")
    print(f"   python test_simple_trace.py  # Prueba simple")
    print(f"   python check_traces.py       # Verificar trazas")
    
    return passed == len(steps)

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️ Diagnóstico interrumpido")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n💥 Error inesperado: {e}")
        import traceback
        print(f"🔍 Traceback: {traceback.format_exc()}")
        sys.exit(1)
