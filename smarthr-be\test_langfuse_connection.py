#!/usr/bin/env python3
"""
Test script to verify Lang<PERSON> connection and integration.
Run this script to check if <PERSON><PERSON> is properly configured and connected.
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the current directory to Python path to import local modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.langfuse_utils import LangfuseManager, get_langfuse_callback_handler
from langchain_core.messages import HumanMessage
from langchain_core.prompts import ChatPromptTemplate

def test_langfuse_configuration():
    """Test if Langfuse configuration is properly set up."""
    print("🔧 Testing Langfuse Configuration...")
    
    required_vars = ['LANGFUSE_PUBLIC_KEY', 'LANGFUSE_SECRET_KEY']
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing environment variables: {', '.join(missing_vars)}")
        print("   Please set these in your .env file")
        return False
    else:
        print("✅ All required environment variables are set")
        return True

def test_langfuse_initialization():
    """Test if Langfuse client can be initialized."""
    print("\n🚀 Testing Langfuse Initialization...")
    
    try:
        success = LangfuseManager.initialize()
        if success:
            print("✅ Langfuse client initialized successfully")
            return True
        else:
            print("❌ Langfuse client initialization failed")
            return False
    except Exception as e:
        print(f"❌ Error initializing Langfuse: {str(e)}")
        return False

def test_callback_handler_creation():
    """Test if callback handler can be created."""
    print("\n🔗 Testing Callback Handler Creation...")
    
    try:
        handler = get_langfuse_callback_handler(
            user_id="test-user",
            session_id="test-session",
            tags=["test", "connection-check"]
        )
        
        if handler is not None:
            print("✅ Callback handler created successfully")
            return True, handler
        else:
            print("❌ Callback handler creation returned None")
            return False, None
    except Exception as e:
        print(f"❌ Error creating callback handler: {str(e)}")
        return False, None

def test_simple_trace():
    """Test creating a simple trace."""
    print("\n📊 Testing Simple Trace Creation...")
    
    try:
        with LangfuseManager.trace_context(
            name="langfuse-connection-test",
            user_id="test-user",
            session_id="test-session",
            tags=["connection-test"],
            input_data={"test": "connection check"}
        ) as span:
            if span is not None:
                print("✅ Trace context created successfully")
                span.update_trace(output={"status": "success", "message": "Connection test completed"})
                return True
            else:
                print("⚠️  Trace context created but span is None (Langfuse may be disabled)")
                return False
    except Exception as e:
        print(f"❌ Error creating trace: {str(e)}")
        return False

def test_with_mock_langchain():
    """Test integration with a mock LangChain operation."""
    print("\n🔄 Testing LangChain Integration...")
    
    try:
        # Get callback handler
        handler = get_langfuse_callback_handler(
            user_id="test-user",
            session_id="test-session-langchain",
            tags=["langchain-test"]
        )
        
        if handler is None:
            print("⚠️  No callback handler available (Langfuse may be disabled)")
            return False
        
        # Create a simple prompt template (this won't actually call an LLM)
        prompt = ChatPromptTemplate.from_template("Test prompt: {input}")
        
        # Format the prompt (this will be traced by Langfuse)
        formatted = prompt.format(input="connection test")
        
        print("✅ LangChain integration test completed")
        print(f"   Formatted prompt: {formatted[:50]}...")
        return True
        
    except Exception as e:
        print(f"❌ Error in LangChain integration test: {str(e)}")
        return False

def main():
    """Run all connection tests."""
    print("🧪 Langfuse Connection Test Suite")
    print("=" * 50)
    
    tests = [
        ("Configuration", test_langfuse_configuration),
        ("Initialization", test_langfuse_initialization),
        ("Callback Handler", lambda: test_callback_handler_creation()[0]),
        ("Simple Trace", test_simple_trace),
        ("LangChain Integration", test_with_mock_langchain)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {str(e)}")
            results.append((test_name, False))
    
    # Flush events to ensure they're sent to Langfuse
    print("\n📤 Flushing events to Langfuse...")
    try:
        LangfuseManager.flush()
        print("✅ Events flushed successfully")
    except Exception as e:
        print(f"⚠️  Error flushing events: {str(e)}")
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 Test Results Summary:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 All tests passed! Langfuse is properly connected.")
        print("   You can now check your Langfuse dashboard for the test traces.")
        print(f"   Dashboard URL: {os.getenv('LANGFUSE_HOST', 'https://cloud.langfuse.com')}")
    else:
        print("\n⚠️  Some tests failed. Please check your configuration.")
        print("   Common issues:")
        print("   - Missing or incorrect API keys")
        print("   - Network connectivity issues")
        print("   - Incorrect LANGFUSE_HOST URL")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
