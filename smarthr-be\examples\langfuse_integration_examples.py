#!/usr/bin/env python3
"""
Examples showing how to use Langfuse tracing with SmartHR LLM operations.

This file demonstrates various ways to integrate Langfuse tracing into your
SmartHR application for comprehensive LLM observability.
"""

from typing import List, Optional
from langchain_core.messages import HumanMessage

# Import your existing functions
from models.llm import inference_with_fallback
from utils.match_evaluations import evaluate_candidate, evaluate_candidates_batch
from utils.langfuse_utils import LangfuseManager, get_langfuse_callback_handler
from controllers.interview_controller import process_interview_transcript


def example_basic_llm_call_with_tracing():
    """
    Example 1: Basic LLM call with Langfuse tracing
    """
    print("=== Example 1: Basic LLM Call with Tracing ===")
    
    # Simple LLM call with tracing
    result = inference_with_fallback(
        task_prompt="You are a helpful HR assistant. Analyze the following job description and extract key requirements.",
        user_messages=[HumanMessage(content="Software Engineer position requiring Python, FastAPI, and 3+ years experience.")],
        models_order=["gpt-4o-mini"],
        # Langfuse tracing parameters
        user_id="hr-manager-123",
        session_id="job-analysis-session-456",
        trace_tags=["job-analysis", "requirements-extraction"],
        trace_name="job-requirements-analysis"
    )
    
    print(f"Result: {result.content if hasattr(result, 'content') else result}")
    return result


def example_candidate_evaluation_with_tracing():
    """
    Example 2: Candidate evaluation with comprehensive tracing
    """
    print("\n=== Example 2: Candidate Evaluation with Tracing ===")
    
    candidate_text = """
    John Doe - Senior Software Engineer
    - 5 years Python development experience
    - Expert in FastAPI, Django, Flask
    - Led team of 4 developers
    - Built scalable microservices architecture
    """
    
    position_text = """
    Senior Python Developer
    - 3+ years Python experience required
    - FastAPI framework knowledge essential
    - Team leadership experience preferred
    - Microservices architecture experience
    """
    
    # Evaluate candidate with tracing
    result = evaluate_candidate(
        candidate=candidate_text,
        position=position_text,
        models_order=["gpt-4o-mini"],
        # Tracing parameters
        user_id="recruiter-789",
        session_id="candidate-review-session-101",
        candidate_id="candidate-john-doe-456",
        position_id="position-senior-python-123"
    )
    
    print(f"Evaluation Score: {result.get('Score', 'N/A')}")
    print(f"Analysis: {result.get('LLM_Analysis', {})}")
    return result


def example_batch_evaluation_with_tracing():
    """
    Example 3: Batch candidate evaluation with tracing
    """
    print("\n=== Example 3: Batch Evaluation with Tracing ===")
    
    candidates = [
        "Alice Smith - 4 years Python, FastAPI expert, team lead experience",
        "Bob Johnson - 2 years Python, Django focus, junior developer",
        "Carol Williams - 6 years Python, microservices architect, senior role"
    ]
    
    position = "Senior Python Developer - 3+ years experience, FastAPI, team leadership"
    
    # Batch evaluation with tracing
    result = evaluate_candidates_batch(
        candidates=candidates,
        position=position,
        models_order=["gpt-4o-mini"],
        # Tracing parameters
        user_id="hr-director-555",
        session_id="batch-evaluation-session-202",
        position_id="position-senior-python-123"
    )
    
    print(f"Batch Analysis Summary: {result.summary if hasattr(result, 'summary') else 'N/A'}")
    print(f"Number of candidates analyzed: {len(result.candidates_analysis) if hasattr(result, 'candidates_analysis') else 0}")
    return result


def example_interview_processing_with_tracing():
    """
    Example 4: Interview transcript processing with tracing
    """
    print("\n=== Example 4: Interview Processing with Tracing ===")
    
    # This would typically be called from your interview controller
    # but here's how you could add tracing context
    
    with LangfuseManager.trace_context(
        name="interview-transcript-processing",
        user_id="interviewer-333",
        session_id="interview-session-789",
        tags=["interview", "transcript-analysis", "technical-interview"],
        input_data={
            "interview_type": "technical",
            "candidate_id": "candidate-456",
            "position_id": "position-123"
        }
    ) as span:
        
        # Your existing interview processing logic would go here
        # The LLM calls inside will automatically be traced under this span
        
        print("Processing interview transcript...")
        
        # Example of updating the trace with results
        if span:
            span.update_trace(
                output={
                    "status": "completed",
                    "questions_answered": 5,
                    "technical_score": 85
                }
            )
        
        return {"status": "processed", "score": 85}


def example_position_analysis_with_tracing():
    """
    Example 5: Position analysis with custom tracing
    """
    print("\n=== Example 5: Position Analysis with Tracing ===")
    
    position_raw = """
    We are looking for a Senior Full Stack Developer to join our team.
    Requirements: React, Node.js, PostgreSQL, 5+ years experience.
    Responsibilities: Lead development team, architect solutions, mentor juniors.
    """
    
    # Using the trace context manager for custom operations
    with LangfuseManager.trace_context(
        name="position-analysis-pipeline",
        user_id="hr-admin-777",
        session_id="position-setup-session-303",
        tags=["position-analysis", "job-posting", "requirements-extraction"],
        input_data={"raw_position": position_raw[:100] + "..."}
    ) as span:
        
        # Step 1: Extract structured information
        structured_info = inference_with_fallback(
            task_prompt="Extract structured information from this job posting including requirements, responsibilities, and qualifications.",
            user_messages=[HumanMessage(content=position_raw)],
            models_order=["gpt-4o-mini"],
            user_id="hr-admin-777",
            session_id="position-setup-session-303",
            trace_tags=["position-structuring"],
            trace_name="position-structure-extraction"
        )
        
        # Step 2: Generate embedding text
        embedding_text = inference_with_fallback(
            task_prompt="Clean up and optimize this position description for embedding and candidate matching.",
            user_messages=[HumanMessage(content=position_raw)],
            models_order=["gpt-4o-mini"],
            user_id="hr-admin-777",
            session_id="position-setup-session-303",
            trace_tags=["embedding-optimization"],
            trace_name="position-embedding-prep"
        )
        
        # Update trace with final results
        if span:
            span.update_trace(
                output={
                    "structured_info_extracted": bool(structured_info),
                    "embedding_text_generated": bool(embedding_text),
                    "pipeline_status": "completed"
                }
            )
        
        print("Position analysis completed with full tracing")
        return {
            "structured_info": structured_info,
            "embedding_text": embedding_text
        }


def example_session_based_tracing():
    """
    Example 6: Session-based tracing for related operations
    """
    print("\n=== Example 6: Session-based Tracing ===")
    
    session_id = "hiring-pipeline-session-404"
    user_id = "hiring-manager-999"
    
    # Multiple related operations under the same session
    operations = [
        ("position-analysis", "Analyze job requirements"),
        ("candidate-search", "Find matching candidates"),
        ("initial-screening", "Screen top candidates")
    ]
    
    results = []
    for operation_type, description in operations:
        result = inference_with_fallback(
            task_prompt=f"You are an HR assistant. {description}.",
            user_messages=[HumanMessage(content="Sample HR task content")],
            models_order=["gpt-4o-mini"],
            user_id=user_id,
            session_id=session_id,  # Same session for all operations
            trace_tags=["hiring-pipeline", operation_type],
            trace_name=operation_type
        )
        results.append(result)
        print(f"Completed: {operation_type}")
    
    print(f"All operations completed under session: {session_id}")
    return results


def run_all_examples():
    """
    Run all examples to demonstrate Langfuse integration
    """
    print("🚀 Running SmartHR Langfuse Integration Examples")
    print("=" * 60)
    
    try:
        # Initialize Langfuse
        if not LangfuseManager.initialize():
            print("⚠️  Langfuse not configured. Examples will run without tracing.")
        
        # Run examples
        example_basic_llm_call_with_tracing()
        example_candidate_evaluation_with_tracing()
        example_batch_evaluation_with_tracing()
        example_interview_processing_with_tracing()
        example_position_analysis_with_tracing()
        example_session_based_tracing()
        
        print("\n" + "=" * 60)
        print("✅ All examples completed!")
        print("Check your Langfuse dashboard to see the traces:")
        print(f"   Dashboard: {LangfuseManager.get_client()._host if LangfuseManager.get_client() else 'https://cloud.langfuse.com'}")
        
        # Flush events to ensure they're sent
        LangfuseManager.flush()
        
    except Exception as e:
        print(f"❌ Error running examples: {str(e)}")
    
    finally:
        # Clean shutdown
        LangfuseManager.shutdown()


if __name__ == "__main__":
    run_all_examples()
