#!/usr/bin/env python3
"""
Script para verificar si las trazas están llegando a Langfuse.
Este script crea trazas de prueba y luego verifica si aparecen en el dashboard.
"""

import os
import sys
import time
import uuid
from datetime import datetime
from dotenv import load_dotenv

load_dotenv()

def create_test_traces():
    """Crear varias trazas de prueba con diferentes métodos"""
    print("🧪 Creando Trazas de Prueba")
    print("-" * 40)
    
    try:
        from langfuse import Langfuse
        
        # Inicializar cliente
        langfuse = Langfuse(
            public_key=os.getenv('LANGFUSE_PUBLIC_KEY'),
            secret_key=os.getenv('LANGFUSE_SECRET_KEY'),
            host=os.getenv('LANGFUSE_HOST', 'https://cloud.langfuse.com')
        )
        
        test_id = str(uuid.uuid4())[:8]
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        traces_created = []
        
        # Traza 1: Traza simple
        print("📊 Creando traza simple...")
        trace1 = langfuse.trace(
            name=f"test-simple-{test_id}",
            input={"test": "simple trace", "timestamp": timestamp},
            metadata={"type": "simple", "test_id": test_id}
        )
        trace1.update(output={"status": "completed", "result": "simple trace created"})
        traces_created.append(("Simple", trace1.id))
        
        # Traza 2: Traza con span
        print("📊 Creando traza con span...")
        trace2 = langfuse.trace(
            name=f"test-with-span-{test_id}",
            input={"test": "trace with span", "timestamp": timestamp},
            metadata={"type": "with_span", "test_id": test_id}
        )
        
        span = trace2.span(
            name="test-span",
            input={"operation": "test span"},
            output={"result": "span completed"}
        )
        
        trace2.update(output={"status": "completed", "spans": 1})
        traces_created.append(("Con Span", trace2.id))
        
        # Traza 3: Traza con generation (simulando LLM)
        print("📊 Creando traza con generation...")
        trace3 = langfuse.trace(
            name=f"test-llm-simulation-{test_id}",
            input={"prompt": "Test LLM call", "timestamp": timestamp},
            metadata={"type": "llm_simulation", "test_id": test_id}
        )
        
        generation = trace3.generation(
            name="test-generation",
            model="test-model",
            input=[{"role": "user", "content": "Test message"}],
            output={"role": "assistant", "content": "Test response"},
            usage={"input_tokens": 10, "output_tokens": 5, "total_tokens": 15}
        )
        
        trace3.update(output={"status": "completed", "response": "Test response"})
        traces_created.append(("Con Generation", trace3.id))
        
        # Traza 4: Traza con callback handler
        print("📊 Creando traza con callback handler...")
        try:
            from langfuse.langchain import CallbackHandler
            from langchain_core.messages import HumanMessage
            
            handler = CallbackHandler()
            
            # Simular una operación LangChain
            trace4 = langfuse.trace(
                name=f"test-callback-{test_id}",
                input={"method": "callback_handler", "timestamp": timestamp},
                metadata={"type": "callback", "test_id": test_id}
            )
            
            # Simular que el callback handler está funcionando
            trace4.update(output={"status": "callback_ready", "handler": "created"})
            traces_created.append(("Con Callback", trace4.id))
            
        except Exception as e:
            print(f"⚠️ Error con callback handler: {e}")
        
        # Flush todas las trazas
        print("📤 Enviando todas las trazas...")
        langfuse.flush()
        
        print(f"✅ {len(traces_created)} trazas creadas y enviadas")
        
        for trace_type, trace_id in traces_created:
            print(f"  - {trace_type}: {trace_id}")
        
        return traces_created, test_id
        
    except Exception as e:
        print(f"❌ Error creando trazas: {e}")
        import traceback
        print(f"🔍 Traceback: {traceback.format_exc()}")
        return [], None

def wait_and_check():
    """Esperar y dar instrucciones para verificar"""
    print("\n⏳ Esperando para que se procesen las trazas...")
    
    for i in range(10, 0, -1):
        print(f"  ⏱️ {i} segundos restantes...", end="\r")
        time.sleep(1)
    
    print("\n✅ Tiempo de espera completado")

def provide_check_instructions(traces_created, test_id):
    """Proporcionar instrucciones para verificar las trazas"""
    print("\n🔍 INSTRUCCIONES PARA VERIFICAR LAS TRAZAS")
    print("=" * 50)
    
    dashboard_url = os.getenv('LANGFUSE_HOST', 'https://cloud.langfuse.com')
    
    print(f"1. 🌐 Ve a tu dashboard de Langfuse:")
    print(f"   {dashboard_url}")
    
    print(f"\n2. 🔍 Busca las siguientes trazas (creadas ahora):")
    for trace_type, trace_id in traces_created:
        print(f"   - {trace_type}: {trace_id}")
    
    print(f"\n3. 🏷️ O busca por el test_id en metadata:")
    print(f"   test_id: {test_id}")
    
    print(f"\n4. 📅 O filtra por trazas creadas en los últimos minutos")
    
    print(f"\n5. 🔎 Nombres de trazas a buscar:")
    print(f"   - test-simple-{test_id}")
    print(f"   - test-with-span-{test_id}")
    print(f"   - test-llm-simulation-{test_id}")
    print(f"   - test-callback-{test_id}")

def test_real_llm_call():
    """Hacer una llamada LLM real con tracing"""
    print("\n🤖 PRUEBA CON LLAMADA LLM REAL")
    print("-" * 40)
    
    try:
        from langfuse import Langfuse
        from langfuse.langchain import CallbackHandler
        
        # Inicializar
        langfuse = Langfuse(
            public_key=os.getenv('LANGFUSE_PUBLIC_KEY'),
            secret_key=os.getenv('LANGFUSE_SECRET_KEY'),
            host=os.getenv('LANGFUSE_HOST', 'https://cloud.langfuse.com')
        )
        
        handler = CallbackHandler()
        
        # Verificar si tenemos configuración de Azure OpenAI
        if not os.getenv('AZURE_OPENAI_API_KEY'):
            print("⚠️ No hay configuración de Azure OpenAI, saltando prueba LLM real")
            return False
        
        from langchain_openai import AzureChatOpenAI
        from langchain_core.messages import HumanMessage
        
        llm = AzureChatOpenAI(
            deployment_name="gpt-4o-mini-smarthr",
            temperature=0
        )
        
        test_id = str(uuid.uuid4())[:8]
        
        print(f"🚀 Haciendo llamada LLM real con tracing (ID: {test_id})...")
        
        result = llm.invoke(
            [HumanMessage(content=f"Responde solo con 'TRAZA REAL FUNCIONA - {test_id}' si recibes este mensaje.")],
            config={
                "callbacks": [handler],
                "run_name": f"real-llm-test-{test_id}",
                "metadata": {
                    "test_type": "real_llm",
                    "test_id": test_id,
                    "timestamp": datetime.now().isoformat()
                }
            }
        )
        
        print(f"✅ LLM respondió: {result.content}")
        
        # Flush
        langfuse.flush()
        
        print(f"📊 Busca en tu dashboard la traza: real-llm-test-{test_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error en llamada LLM real: {e}")
        import traceback
        print(f"🔍 Traceback: {traceback.format_exc()}")
        return False

def main():
    """Función principal"""
    print("🔬 VERIFICACIÓN DE TRAZAS LANGFUSE")
    print("=" * 50)
    
    # Verificar configuración
    required_vars = ['LANGFUSE_PUBLIC_KEY', 'LANGFUSE_SECRET_KEY']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"❌ Variables faltantes: {', '.join(missing_vars)}")
        return False
    
    print("✅ Variables de entorno configuradas")
    
    # Crear trazas de prueba
    traces_created, test_id = create_test_traces()
    
    if not traces_created:
        print("❌ No se pudieron crear trazas de prueba")
        return False
    
    # Probar llamada LLM real
    llm_success = test_real_llm_call()
    
    # Esperar
    wait_and_check()
    
    # Proporcionar instrucciones
    provide_check_instructions(traces_created, test_id)
    
    print(f"\n🎯 RESUMEN:")
    print(f"  - Trazas de prueba creadas: {len(traces_created)}")
    print(f"  - Llamada LLM real: {'✅ Exitosa' if llm_success else '❌ Falló'}")
    print(f"  - Test ID: {test_id}")
    
    print(f"\n💡 PRÓXIMOS PASOS:")
    print(f"  1. Revisa tu dashboard de Langfuse")
    print(f"  2. Si no ves las trazas, verifica tu conectividad")
    print(f"  3. Si las ves, ¡la integración está funcionando! 🎉")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️ Verificación interrumpida")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n💥 Error inesperado: {e}")
        import traceback
        print(f"🔍 Traceback: {traceback.format_exc()}")
        sys.exit(1)
