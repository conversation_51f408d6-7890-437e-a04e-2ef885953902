from datetime import datetime
from pydantic import BaseModel, Field
from typing import List, Optional
from enum import Enum


# ─────────────────────────────── core enums ───────────────────────────────────
class ProcessType(str, Enum):
    EXTRACT = "extract"
    PARAPHRASE = "paraphrase"


class Seniority(str, Enum):
    SENIOR = "senior"
    MID = "mid"
    JUNIOR = "junior"
    NA = "n/a"


class InterviewStatus(str, Enum):
    PENDING = "pending"
    SCHEDULED = "scheduled"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


# ─────────────────────────── request/response models ──────────────────────────
class InterviewProcessingRequest(BaseModel):
    questions: List[str]
    transcript: str
    process_type: ProcessType


class ExtractedAnswers(BaseModel):
    answers: List[str]


class ParaphrasedAnswerDetail(BaseModel):
    answer: str
    paraphrased: str
    complement_from: Optional[str] = None
    # complement_from: Optional[List[str]] = []


class ParaphrasedAnswers(BaseModel):
    answers: List[ParaphrasedAnswerDetail]


# ───────────────────────── questionnaire models ───────────────────────────────
class QuestionAnswer(BaseModel):
    question_number: int = Field(..., description="Question number for tracking")
    question: str
    senior_answer: str
    mid_answer: str
    junior_answer: str
    tag: str = Field(..., description="Tag to uniquely categorize the question with only one of the following: 'Technical Skills', 'Soft Skills', 'Methodologies', 'Language - Tools'")


class QA_model(BaseModel):
    questions: List[QuestionAnswer]


# ───────────────────── evaluation (scoring) models ────────────────────────────
class QuestionEvaluation(BaseModel):
    question_number: int
    expected_seniority: Seniority
    detected_seniority: Seniority
    explanation: str = Field(..., description="Explanation of the evaluation, including reasoning for the detected seniority level.")


class EvaluateInterviewNoQA(BaseModel):
    overall_seniority: Seniority
    percentage_of_match: float
    explanation: str = Field(..., description="Explanation of the evaluation, including reasoning for the overall seniority and percentage of match.")


class EvaluationResult(BaseModel):
    overall_seniority: Seniority
    per_question: List[QuestionEvaluation]
    percentage_of_match: float
    explanation: str = Field(..., description="Overall explanation of the evaluation, including reasoning for the overall seniority and percentage of match. Include the feedback tec comments in the last paragraph of the evaluation.")


# ─────────────────────────────── interview models ──────────────────────────────
class InterviewBase(BaseModel):
    id: str
    position_id: str
    candidate_id: str
    candidate_info: Optional[dict] = None
    position_info: Optional[dict] = None
    interview_data: Optional[dict] = None
    anwers_data: Optional[dict] = None
    created_at: datetime
    updated_at: datetime


class Interview(InterviewBase):
    feedback_hr: Optional[dict] = None
    recruiter_hr_id: Optional[str] = None
    scheduled_hr_id: Optional[str] = None
    interview_date_hr: Optional[datetime] = None
    feedback_date_hr: Optional[datetime] = None
    status_hr: Optional[str] = None
    recommendation_hr: Optional[bool] = None
    transcript_hr: Optional[str] = None
    feedback_tec: Optional[dict] = None  
    recruiter_tec_id: Optional[str] = None
    scheduled_tec_id: Optional[str] = None
    interview_date_tec: Optional[datetime] = None
    feedback_date_tec: Optional[datetime] = None
    status_tec: Optional[str] = None
    recommendation_tec: Optional[bool] = None
    transcript_tec: Optional[str] = None,
    analysis_data: Optional[dict] = None  # Analysis data for the interview


class InterviewHr(BaseModel):
    position_id: str
    candidate_id: str 
    recruiter_hr_id: str
    scheduled_hr_id: str  
    feedback_hr: dict
    interview_date_hr: datetime
    feedback_date_hr: datetime
    status_hr: str
    recommendation_hr: bool
    transcript_hr: str


class InterviewTec(BaseModel):
    position_id: str
    candidate_id: str  
    recruiter_tec_id: str
    scheduled_tec_id: str 
    feedback_tec: dict
    interview_date_tec: datetime
    feedback_date_tec: datetime
    status_tec: str
    recommendation_tec: bool
    transcript_tec: str


class InterviewCreate(BaseModel):
    candidate_id: str
    analysis_data: dict  # Analysis data for the interview
